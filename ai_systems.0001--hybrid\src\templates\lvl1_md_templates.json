{"catalog_meta": {"level": "lvl1", "format": "md", "generated_at": "2025.05.25-kl.23.45", "source_directory": "lvl1/md", "total_templates": 5, "total_sequences": 2}, "templates": {"0001-a-rephrase-instructionconverter": {"raw": "[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "parts": {"title": "Instruction Converter", "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:", "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "keywords": "inherent"}}, "0001-b-essence-distillation": {"raw": "[Essence Distillation] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "parts": {"title": "Essence Distillation", "interpretation": "Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.", "transformation": "`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "keywords": "distill|maximally|essence|elegant"}}, "0002-a-essence-distillation": {"raw": "[Essence Distillation] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "parts": {"title": "Essence Distillation", "interpretation": "Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.", "transformation": "`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "keywords": "distill|maximally|essence|elegant"}}, "0002-b-exposing-coherence": {"raw": "[Exposing Coherence] Your task is not complexification but simplification; expose the input's underlying logical coherence by removing redundancy while strictly preserving the core message. `{role=coherence_exposer; input=[content:str]; process=[analyze_logical_flow(), identify_redundancies(), map_core_arguments(), simplify_language(preserve_meaning=True), restructure_for_intuitive_clarity(), verify_message_integrity()]; output={simplified_content:str}}`", "parts": {"title": "Exposing Coherence", "interpretation": "Your task is not complexification but simplification; expose the input's underlying logical coherence by removing redundancy while strictly preserving the core message.", "transformation": "`{role=coherence_exposer; input=[content:str]; process=[analyze_logical_flow(), identify_redundancies(), map_core_arguments(), simplify_language(preserve_meaning=True), restructure_for_intuitive_clarity(), verify_message_integrity()]; output={simplified_content:str}}`", "keywords": "coherence"}}, "0002-c-precision-enhancement": {"raw": "[Precision Enhancement] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "parts": {"title": "Precision Enhancement", "interpretation": "Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail.", "transformation": "`{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`", "keywords": "distill|maximally|essence|elegant"}}}, "sequences": {"0001": [{"template_id": "0001-a-rephrase-instructionconverter", "step": "a", "order": 0}, {"template_id": "0001-b-essence-distillation", "step": "b", "order": 1}], "0002": [{"template_id": "0002-a-essence-distillation", "step": "a", "order": 0}, {"template_id": "0002-b-exposing-coherence", "step": "b", "order": 1}, {"template_id": "0002-c-precision-enhancement", "step": "c", "order": 2}]}}